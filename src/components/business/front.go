package business

import (
	"fmt"
	"image"
	"image/draw"
	"io"
	"os"
	"path"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/qr"
	"github.com/gin-gonic/gin"
)

var orderTypeTextMap = map[constmap.OrderType]string{
	constmap.OrderTypeTravel: "套餐",
	constmap.OrderTypeHotel:  "酒店",
	constmap.OrderTypeTuan:   "团游",
	constmap.OrderTypeTicket: "门票",
}
var orderSubTypeTextMap = map[constmap.OrderSubType]string{
	constmap.OrderSubTypeTicket: "门票",
	constmap.OrderSubTypeHotel:  "酒店",
	constmap.OrderSubTypeTuan:   "团游",
}

var idTypeTextMap = map[constmap.IdType]string{
	constmap.IdTypeIdCard:           "身份证",
	constmap.IdTypeStudent:          "学生证",
	constmap.IdTypeSoldier:          "军官证",
	constmap.IdTypePassport:         "护照",
	constmap.IdTypeResidenceBooklet: "户口本",
	constmap.IdTypeGAPassport:       "港澳通行证",
	constmap.IdTypeTaiwanResident:   "台胞证",
	constmap.IdTypeTaiwanPassport:   "台湾通行证",
	constmap.IdTypeGreenCard:        "外国人在中国永久居留证",
}

var bannerPositionMap = map[constmap.BannerPosition]string{
	constmap.BannerPositionHome:       "首页",
	constmap.BannerPositionFaXian:     "发现页",
	constmap.BannerPositionWishSquare: "心愿广场",
}

var enableStateTextMap = map[int]string{
	constmap.Enable:  "在线",
	constmap.Disable: "离线",
}

var TaskCondTextMap = map[constmap.TaskCond]string{
	constmap.TaskCondTaskAccIntegral: "任务累计积分",
	constmap.TaskCondSharePlan:       "分享行程",
	constmap.TaskCondSavePlan:        "收藏行程",
	constmap.TaskCondMakePlan:        "行程规划",
	constmap.TaskCondJoinAct:         "参与活动",
	constmap.TaskCondShareAct:        "分享活动",
	constmap.TaskCondShareMini:       "分享小程序",
	constmap.TaskCondSceneSignAll:    "全部景点打卡",
	constmap.TaskCondSceneSign:       "景点打卡",
	constmap.TaskCondRankSettle:      "排行榜结算入榜",
	constmap.TaskCondParent:          "完成父任务",
}

var TaskRewardTextMap = map[constmap.TaskReward]string{
	constmap.TaskRewardActIntegral:     "活动积分",
	constmap.TaskRewardAccountIntegral: "账户积分",
	constmap.TaskRewardAirWifi:         "上网卡",
	constmap.TaskRewardRealProduct:     "实物奖励",
}

var TaskIntervalTextMap = map[constmap.TaskInterval]string{
	constmap.TaskIntervalOnce: "单轮任务",
	constmap.TaskIntervalDay:  "日任务",
	constmap.TaskIntervalWeek: "周任务",
}

var refundOrderStateTextMap = map[constmap.RefundOrderState]string{
	constmap.RefundOrderWaitReview: "待处理",
	constmap.RefundOrderProcessing: "未完成",
	constmap.RefundOrderFinish:     "已完成",
}

var refundSubOrderStateTextMap = map[constmap.RefundSubOrderState]string{
	constmap.RefundSubOrderRefunding:  "退款中",
	constmap.RefundSubOrderRefunded:   "退款成功",
	constmap.RefundSubOrderAbnormal:   "退款异常",
	constmap.RefundSubOrderWaitReview: "待处理",
	constmap.RefundSubOrderWaitRefund: "审核通过",
	constmap.RefundSubOrderReject:     "退款拒绝",
}

var subOrderStateTextMap = map[int]string{
	constmap.OrderDetailStateRefund:     "已退款",
	constmap.OrderDetailStateFail:       "确认失败",
	constmap.OrderDetailStateComplete:   "已完成",
	constmap.OrderDetailStateProcessing: "确认中",
	constmap.OrderDetailStateWaitPay:    "待付款",
	constmap.OrderDetailStateSuccess:    "确认成功",
}

var userTaskRewardStateTextMap = map[constmap.UserTaskRewardState]string{
	constmap.UserTaskRewardWait: "待发货",
	constmap.UserTaskRewardSelf: "自提",
	constmap.UserTaskRewardSend: "已发货",
}

var packageExpUnitTextMap = map[constmap.PackageExpUnit]string{
	constmap.PackageExpUnitWeek:   "周",
	constmap.PackageExpUnitMonth:  "月",
	constmap.PackageExpUnitSeason: "季度",
	constmap.PackageExpUnitYear:   "年",
}

var userPackageStateTextMap = map[constmap.UserPackageState]string{
	constmap.UserPackageStateDefault:  "未生效",
	constmap.UserPackageStateUsing:    "使用中",
	constmap.UserPackageStateComplete: "已使用",
	constmap.UserPackageStateExpired:  "已过期",
	constmap.UserPackageStateAbnormal: "异常",
}

var accountLogSubTextMap = map[constmap.AccountLogSub]string{
	constmap.AccountLogSubBuyPackage:     "购买套餐",
	constmap.AccountLogSubInitUser:       "首次登录",
	constmap.AccountLogSubBindInviteCode: "绑定邀请码",
	constmap.AccountLogSubPlanCost:       "兑换行程规划服务",
	constmap.AccountLogSubPlanPdf:        "导出行程PDF",
	constmap.AccountLogSubShareMini:      "分享小程序",
	constmap.AccountLogSubPackageExpire:  "套餐过期清零",
	constmap.AccountLogSubAdminOperate:   "管理员操作",
	constmap.AccountLogSubAdView:         "看广告得积分",
	constmap.AccountLogSubActReward:      "活动奖励",
}

var accountLogTypeTextMap = map[constmap.AccountLog]string{
	constmap.AccountLogIncrAmount:      "收入",
	constmap.AccountLogDecrAmount:      "支出",
	constmap.AccountLogIncrLockAmount:  "冻结收入",
	constmap.AccountLogDecrLockAmount:  "冻结支出",
	constmap.AccountLogConvAmount2Lock: "转入冻结",
	constmap.AccountLogConvLock2Amount: "解冻转入",
}

var guestTypeTextMap = map[constmap.GuestType]string{
	constmap.GuestTypeMainland: "仅大陆客人",
	constmap.GuestTypeOverseas: "仅大陆和港澳台",
	constmap.GuestTypeAll:      "大陆、港澳台及外国人",
}

var currencyTypeTextMap = map[constmap.CurrencyType]string{
	constmap.CurrencyIntegral: "积分",
}

var wishStateFrontTextMap = map[constmap.WishState]string{
	constmap.WishStateWaitReview: "进行中",
	constmap.WishStateRejected:   "关闭",
	constmap.WishStateProcessing: "进行中",
	constmap.WishStateSuccess:    "已达成",
	constmap.WishStateFinished:   "已去过",
	constmap.WishStateClosed:     "关闭",
}

var wishStateAdminTextMap = map[constmap.WishState]string{
	constmap.WishStateWaitReview: "待审核",
	constmap.WishStateRejected:   "已驳回",
	constmap.WishStateProcessing: "进行中",
	constmap.WishStateSuccess:    "已达成",
	constmap.WishStateFinished:   "已去过",
	constmap.WishStateClosed:     "关闭",
}

var wishFollowStateAdminTextMap = map[constmap.WishFollowState]string{
	constmap.WishFollowStateDefault:        "默认",
	constmap.WishFollowStateWait:           "待跟进",
	constmap.WishFollowStateProcessing:     "跟进中",
	constmap.WishFollowStateTransFinished:  "达成交易",
	constmap.WishFollowStateTransCancelled: "交易取消",
}

var wishMemberStateTextMap = map[constmap.WishMemberState]string{
	constmap.WishMemberStateWaitReview: "待审核",
	constmap.WishMemberStateApproved:   "已通过",
	constmap.WishMemberStateRejected:   "已拒绝",
}

var messageTypeTextMap = map[constmap.MessageType]string{
	constmap.MessageTypeSystem:  "系统消息",
	constmap.MessageTypeTeam:    "组队消息",
	constmap.MessageTypeComment: "评论消息",
}

var messageSubTypeTextMap = map[constmap.MessageSubType]string{
	constmap.MessageSubTypeText:       "普通消息",
	constmap.MessageSubTypeTeamInvite: "邀请加入",
	constmap.MessageSubTypeTeamApply:  "申请加入",
}

var wishCommentStateTextMap = map[constmap.WishCommentState]string{
	constmap.WishCommentStateWaitReview: "待审核",
	constmap.WishCommentStateApproved:   "已通过",
	constmap.WishCommentStateRejected:   "已拒绝",
}

var openScopeTextMap = map[int]string{
	constmap.Enable:  "公开",
	constmap.Disable: "隐藏",
}

func WishStateAdminText(st constmap.WishState) string {
	return utils.If(wishStateAdminTextMap[st] != "", wishStateAdminTextMap[st], constmap.UnknownStr)
}
func WishStateFrontText(st constmap.WishState) string {
	return utils.If(wishStateFrontTextMap[st] != "", wishStateFrontTextMap[st], constmap.UnknownStr)
}

func WishFollowStateAdminText(st constmap.WishFollowState) string {
	return utils.If(wishFollowStateAdminTextMap[st] != "", wishFollowStateAdminTextMap[st], constmap.UnknownStr)
}

func WishMemberStateText(st constmap.WishMemberState) string {
	return utils.If(wishMemberStateTextMap[st] != "", wishMemberStateTextMap[st], constmap.UnknownStr)
}

func CurrencyTypeText(ct constmap.CurrencyType) string {
	return utils.If(currencyTypeTextMap[ct] != "", currencyTypeTextMap[ct], constmap.UnknownStr)
}

func GuestTypeText(gt constmap.GuestType) string {
	return utils.If(guestTypeTextMap[gt] != "", guestTypeTextMap[gt], constmap.UnknownStr)
}

// AccountLogTypeText 返回账户流水类型中文
func AccountLogTypeText(typ constmap.AccountLog) string {
	return utils.If(accountLogTypeTextMap[typ] != "", accountLogTypeTextMap[typ], constmap.UnknownStr)
}
func AccountLogSubText(sub constmap.AccountLogSub) string {
	return utils.If(accountLogSubTextMap[sub] != "", accountLogSubTextMap[sub], constmap.UnknownStr)
}

func UserTaskRewardStateText(state constmap.UserTaskRewardState) string {
	return utils.If(userTaskRewardStateTextMap[state] != "", userTaskRewardStateTextMap[state], constmap.UnknownStr)
}

func SubOrderStateText(state int) string {
	text, ok := subOrderStateTextMap[state]
	if ok {
		return text
	}

	return constmap.UnknownStr
}

func RefundSubOrderStateText(state constmap.RefundSubOrderState) string {
	return utils.If(refundSubOrderStateTextMap[state] != "", refundSubOrderStateTextMap[state], constmap.UnknownStr)
}

func OrderSubTypeText(typ constmap.OrderSubType) string {
	return utils.If(orderSubTypeTextMap[typ] != "", orderSubTypeTextMap[typ], constmap.UnknownStr)
}

func RefundOrderStateText(state constmap.RefundOrderState) string {
	return utils.If(refundOrderStateTextMap[state] != "", refundOrderStateTextMap[state], constmap.UnknownStr)
}

func TaskIntervalText(interval constmap.TaskInterval) string {
	return utils.If(TaskIntervalTextMap[interval] != "", TaskIntervalTextMap[interval], constmap.UnknownStr)
}

func TaskRewardText(rewardType constmap.TaskReward) string {
	return utils.If(TaskRewardTextMap[rewardType] != "", TaskRewardTextMap[rewardType], constmap.UnknownStr)
}

func TaskCondText(cond constmap.TaskCond) string {
	return utils.If(TaskCondTextMap[cond] != "", TaskCondTextMap[cond], constmap.UnknownStr)
}

func EnableStateText(state int) string {
	return utils.If(enableStateTextMap[state] != "", enableStateTextMap[state], constmap.UnknownStr)
}

func BannerPositionText(pos constmap.BannerPosition) string {
	return utils.If(bannerPositionMap[pos] != "", bannerPositionMap[pos], constmap.UnknownStr)
}

func IdTypeText(tp constmap.IdType) string {
	return utils.If(idTypeTextMap[tp] != "", idTypeTextMap[tp], constmap.UnknownStr)
}

func OrderTypeText(tp constmap.OrderType) string {
	return utils.If(orderTypeTextMap[tp] != "", orderTypeTextMap[tp], constmap.UnknownStr)
}

func PackageExpUnitText(unit constmap.PackageExpUnit) string {
	return utils.If(packageExpUnitTextMap[unit] != "", packageExpUnitTextMap[unit], constmap.UnknownStr)
}

func MessageTypeText(msgType constmap.MessageType) string {
	return utils.If(messageTypeTextMap[msgType] != "", messageTypeTextMap[msgType], constmap.UnknownStr)
}

func MessageSubTypeText(subType constmap.MessageSubType) string {
	return utils.If(messageSubTypeTextMap[subType] != "", messageSubTypeTextMap[subType], constmap.UnknownStr)
}

func WishCommentStateText(state constmap.WishCommentState) string {
	return utils.If(wishCommentStateTextMap[state] != "", wishCommentStateTextMap[state], constmap.UnknownStr)
}

func OpenScopeText(scope int) string {
	return utils.If(openScopeTextMap[scope] != "", openScopeTextMap[scope], constmap.UnknownStr)
}

func UserPackageStateText(state constmap.UserPackageState) string {
	return utils.If(userPackageStateTextMap[state] != "", userPackageStateTextMap[state], constmap.UnknownStr)
}

func GetFrontLoginUser(ctx *gin.Context) (*models.Session, error) {
	u, ok := ctx.Get(constmap.ContextFrontUser)

	if !ok {
		return nil, utils.NewErrorStr(constmap.ErrorNotLogin, constmap.ErrorMsgNotLogin)
	}

	return u.(*models.Session), nil
}

func EncodeQrCode(str string, width, height int) (*image.RGBA, error) {
	qrcode, _ := qr.Encode(str, qr.M, qr.Auto)
	qrcode, _ = barcode.Scale(qrcode, width, height)

	// 创建一个新的图像，用于合并二维码和图片
	finalImg := image.NewRGBA(image.Rect(0, 0, qrcode.Bounds().Dx(), qrcode.Bounds().Dy()))
	// 绘制二维码到最终图像
	draw.Draw(finalImg, qrcode.Bounds(), qrcode, image.Point{}, draw.Src)

	return finalImg, nil
}

func GetSourceType(ctx *gin.Context) int {
	return ctx.GetInt(constmap.ContextSource)
}

func GetTempFilePath(dir, fileName string) (string, string) {
	if config.Config.App.UploadDir == "" {
		return "", ""
	}
	return path.Join(config.Config.App.UploadDir, "resources", dir, fileName), path.Join("resources", dir, fileName)
}
func SaveTempFile(dir, fileName string, r io.Reader) error {
	if config.Config.App.UploadDir == "" {
		return fmt.Errorf("upload_dir config not found")
	}
	absDir := path.Join(config.Config.App.UploadDir, "resources", dir)
	if _, err := os.Stat(absDir); err != nil && os.IsNotExist(err) {
		if err = os.MkdirAll(absDir, os.ModePerm); err != nil {
			return err
		}
	}
	file := path.Join(absDir, fileName)
	fs, err := os.OpenFile(file, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0666)
	if err != nil {
		return err
	}
	defer fs.Close()
	b, err := io.ReadAll(r)
	if err != nil {
		return err
	}
	if _, err := fs.Write(b); err != nil {
		return err
	}
	return nil
}

func BuildResponse(data any, err error) gin.H {
	code := constmap.RequestOk
	msg := "success"

	if err != nil {
		code = constmap.ErrorSystem
		msg = err.Error()
		if appError, ok := err.(constmap.AppError); ok {
			code = appError.Code
		}
	}

	return gin.H{
		"code": code,
		"data": data,
		"msg":  msg,
	}
}

func BuildStaticPath(prefix constmap.ResourcePath, file string) string {
	return path.Join(constmap.QiniuPrefix, utils.If(config.IsProduction(), "prod", "dev"), string(prefix), file)
}

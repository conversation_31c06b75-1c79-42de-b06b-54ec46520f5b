package user_wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 心愿单成员列表
func MemberList(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 验证心愿单是否存在并检查权限
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 2. 验证只有发起人可以查看成员列表
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有发起人可以查看成员列表")
	}

	// 3. 查询所有成员
	var members []models.WishMember
	err = db.Where("wish_id = ?", in.WishId).
		Order("created_at ASC").
		Find(&members).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 4. 收集用户ID
	var userIds []uint
	for _, member := range members {
		userIds = append(userIds, member.UserId)
	}

	// 5. 批量查询用户信息
	userMap := user_biz.LoadUsers(db, userIds)

	// 6. 组装返回数据
	type MemberItem struct {
		MemberId  uint   `json:"member_id"`
		UserId    uint   `json:"user_id"`
		Nickname  string `json:"nickname"`
		Avatar    string `json:"avatar"`
		RealName  string `json:"real_name"`
		Phone     string `json:"phone"`
		IsOwner   int    `json:"is_owner"`
		State     int    `json:"state"`
		StateName string `json:"state_name"`
		Remark    string `json:"remark,omitempty"` // 拒绝原因等备注
		JoinTime  int64  `json:"join_time"`
	}

	var list []MemberItem
	for _, member := range members {
		user, exists := userMap[member.UserId]
		if !exists {
			continue // 用户不存在，跳过
		}

		// 状态名称映射
		stateName := business.WishMemberStateText(member.State)

		item := MemberItem{
			MemberId:  member.ID,
			UserId:    member.UserId,
			Nickname:  user.Nickname,
			Avatar:    utils.AvatarUrl(user.Avatar),
			RealName:  member.RealName,
			Phone:     member.Phone,
			IsOwner:   member.IsOwner,
			State:     int(member.State),
			StateName: stateName,
			Remark:    member.Remark,
			JoinTime:  member.CreatedAt.Unix(),
		}

		list = append(list, item)
	}

	var out struct {
		WishId uint         `json:"wish_id"`
		List   []MemberItem `json:"list"`
		Total  int          `json:"total"`
	}
	out.WishId = in.WishId
	out.List = list
	out.Total = len(list)

	return out, nil
}

package wish

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/like_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
)

func Detail(ctx *gin.Context) (any, error) {
	// 定义输入参数结构
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	// 查询心愿单主数据及关联数据
	var wish models.Wish
	if err := db.Preload("Todos").Preload("Members").Preload("Medias").Preload("Ext").Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 查询用户信息
	userMap := user_biz.LoadUsers(db, []uint{wish.UserId})

	// 查询同行人用户信息
	memberUserIds := slice.Map(wish.Members, func(index int, member models.WishMember) uint {
		return member.UserId
	})
	memberUserMap := user_biz.LoadUsers(db, memberUserIds)

	// 查询标签信息
	var tags []string
	if wish.TagIds.Data != nil && len(*wish.TagIds.Data) > 0 {
		tagMap := wish_biz.LoadWishTags(db, *wish.TagIds.Data)
		tags = slice.Map(maputil.Values(tagMap), func(index int, tag *models.WishTag) string {
			return tag.Tag
		})
	}

	// 查询关联的行程信息
	var planTop *beans.PlanTop
	var planSections []beans.PlanSection
	if wish.PlanId > 0 {
		// 使用公共方法获取完整的行程信息
		planTop, planSections, _ = plan_asm.BuildPlanSections(db, wish.PlanId, "")
	}

	// 定义响应数据结构
	type memberItem struct {
		Id              uint                     `json:"id"`
		UserId          uint                     `json:"user_id"`
		Nickname        string                   `json:"nickname"`
		Avatar          string                   `json:"avatar"`
		RealName        string                   `json:"real_name"`
		Phone           string                   `json:"phone"`
		IsOwner         int                      `json:"is_owner"`
		Budget          string                   `json:"budget"`
		Remark          string                   `json:"remark"`
		State           constmap.WishMemberState `json:"state"`
		StateText       string                   `json:"state_text"`
		FollowState     constmap.WishFollowState `json:"follow_state"`
		FollowStateText string                   `json:"follow_state_text"`
		CanReview       bool                     `json:"can_review"` // 是否可操作审核
		CreatedAt       int64                    `json:"created_at"`
	}

	type todoItem struct {
		Id     uint   `json:"id"`
		Todo   string `json:"todo"`
		IsMust int    `json:"is_must"`
	}

	type mediaItem struct {
		Id        uint                   `json:"id"`
		MediaType constmap.WishMediaType `json:"media_type"`
		Size      int64                  `json:"size"`
		ResUrl    string                 `json:"res_url"`
	}

	type planItem struct {
		Id       uint                   `json:"id"`
		Subject  string                 `json:"subject"`  // 来自 PlanTop
		Subtitle string                 `json:"subtitle"` // 来自 PlanTop
		FitFor   string                 `json:"fit_for"`  // 来自 PlanTop
		Notice   string                 `json:"notice"`   // 来自 PlanTop
		Poster   string                 `json:"poster"`   // 来自 PlanTop
		Sections []beans.ApiPlanSection `json:"sections"` // 来自 BuildPlanSections，使用API专用结构体
	}

	var out struct {
		// 心愿基本信息
		WishId        uint                    `json:"wish_id"`
		UserId        uint                    `json:"user_id"`
		Nickname      string                  `json:"nickname"`
		Avatar        string                  `json:"avatar"`
		Title         string                  `json:"title"`
		Cover         string                  `json:"cover"`
		From          string                  `json:"from"`
		To            string                  `json:"to"`
		ToZoneId      uint                    `json:"to_zone_id"`
		ToZoneName    string                  `json:"to_zone_name"`
		ToPoi         string                  `json:"to_poi"`
		BudgetType    constmap.WishBudgetType `json:"budget_type"`
		Budget        string                  `json:"budget"`
		TotalPeople   int                     `json:"total_people"`
		DepartDate    string                  `json:"depart_date"`
		ReturnDate    string                  `json:"return_date"`
		DateStr       string                  `json:"date_str"`
		Deadline      int64                   `json:"deadline"`
		OpenScope     int                     `json:"open_scope"`
		OpenScopeText string                  `json:"open_scope_text"`
		State         constmap.WishState      `json:"state"`
		StateText     string                  `json:"state_text"`
		Likes         int64                   `json:"likes"`
		RejectReason  string                  `json:"reject_reason"`
		MemberDesc    string                  `json:"member_desc"`
		WishDesc      string                  `json:"wish_desc"`
		Tags          []string                `json:"tags"`
		CreatedAt     int64                   `json:"created_at"`
		UpdatedAt     int64                   `json:"updated_at"`

		// 操作权限
		CanClose   bool `json:"can_close"`   // 是否可关闭
		CanFinish  bool `json:"can_finish"`  // 可标记已去过
		CanApprove bool `json:"can_approve"` // 可审核通过

		// 关联数据
		Members []memberItem `json:"members"` // 同行人信息
		Todos   []todoItem   `json:"todos"`   // 心愿事项
		Medias  []mediaItem  `json:"medias"`  // 媒体文件

		// 扩展信息
		RiskCheck string `json:"risk_check"` // 风险检测结果

		// 关联行程信息
		Plan *planItem `json:"plan"` // 关联的行程信息（如果有）
	}

	likeMap := like_biz.LoadLikes(db, constmap.LikeWish, []uint{wish.ID})

	var toZone models.Zone
	db.Take(&toZone, wish.ToZoneId)

	// 填充基本信息
	out.WishId = wish.ID
	out.UserId = wish.UserId
	out.Title = wish.Title
	out.Cover = utils.StaticUrl(wish.Cover)
	out.From = wish.From
	out.To = wish.To
	out.ToZoneId = wish.ToZoneId
	out.ToZoneName = toZone.Name
	out.ToPoi = wish.ToPoi
	out.BudgetType = wish.BudgetType
	out.Budget = wish.Budget
	out.TotalPeople = wish.TotalPeople
	out.DepartDate = wish.DepartDate
	out.ReturnDate = wish.ReturnDate
	out.DateStr = wish_biz.GetDepartReturnStr(wish.DepartDate, wish.ReturnDate)
	out.Deadline = wish.Deadline.Unix()
	out.OpenScope = wish.OpenScope
	out.OpenScopeText = business.OpenScopeText(wish.OpenScope)
	out.State = wish.State
	out.StateText = business.WishStateAdminText(wish.State)
	out.Likes = likeMap[wish.ID]
	out.RejectReason = wish.RejectReason
	out.MemberDesc = wish.MemberDesc
	out.WishDesc = wish.WishDesc
	out.Tags = tags
	out.CreatedAt = wish.CreatedAt.Unix()
	out.UpdatedAt = wish.UpdatedAt.Unix()

	// 填充用户信息
	if user, exists := userMap[wish.UserId]; exists {
		out.Nickname = user.Nickname
		out.Avatar = utils.AvatarUrl(user.Avatar)
	}

	// 权限控制
	out.CanClose = wish.State == constmap.WishStateWaitReview || wish.State == constmap.WishStateProcessing // 待审核和进行中可以关闭
	out.CanFinish = wish.State == constmap.WishStateSuccess
	out.CanApprove = wish.State == constmap.WishStateRejected // 已驳回状态可以审核通过

	// 组装同行人信息
	out.Members = slice.Map(wish.Members, func(index int, member models.WishMember) memberItem {
		item := memberItem{
			Id:              member.ID,
			UserId:          member.UserId,
			RealName:        member.RealName,
			Phone:           member.Phone,
			IsOwner:         member.IsOwner,
			Budget:          member.Budget,
			Remark:          member.Remark,
			State:           member.State,
			StateText:       business.WishMemberStateText(member.State),
			FollowState:     member.FollowState,
			FollowStateText: business.WishFollowStateAdminText(member.FollowState),
			CanReview:       member.State == constmap.WishMemberStateWaitReview, // 只有待审核状态可以操作
			CreatedAt:       member.CreatedAt.Unix(),
		}

		// 填充同行人用户信息
		if user, exists := memberUserMap[member.UserId]; exists {
			item.Nickname = user.Nickname
			item.Avatar = utils.AvatarUrl(user.Avatar)
		}

		return item
	})

	// 组装心愿事项
	out.Todos = slice.Map(wish.Todos, func(index int, todo models.WishTodo) todoItem {
		return todoItem{
			Id:     todo.ID,
			Todo:   todo.Todo,
			IsMust: todo.IsMust,
		}
	})

	// 组装媒体文件
	out.Medias = slice.Map(wish.Medias, func(index int, media models.WishMedia) mediaItem {
		return mediaItem{
			Id:        media.ID,
			MediaType: media.MediaType,
			Size:      media.Size,
			ResUrl:    utils.StaticUrl(media.ResUrl),
		}
	})

	// 填充扩展信息
	if wish.Ext.ID > 0 {
		out.RiskCheck = wish.Ext.RiskCheck
	}

	// 填充关联行程信息
	if planTop != nil {
		out.Plan = &planItem{
			Id:       wish.PlanId,
			Subject:  planTop.Subject,
			Subtitle: planTop.Subtitle,
			FitFor:   planTop.FitFor,
			Notice:   planTop.Notice,
			Poster:   planTop.Poster,
			Sections: beans.ConvertToApiPlanSectionSlice(planSections),
		}
	}

	return out, nil
}
